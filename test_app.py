#!/usr/bin/env python3
"""
Test script for the Seasonal Stock Analysis app
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import fetch_stock_data, normalize_to_day_of_year, create_seasonal_price_chart, create_normalized_seasonal_chart, create_multi_year_line_chart, create_recent_comparison_chart, create_rsi_chart, calculate_rsi
from datetime import datetime
import pandas as pd

def test_fetch_stock_data():
    """Test the stock data fetching function"""
    print("Testing stock data fetching...")
    
    # Test with a simple date range
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    
    try:
        data = fetch_stock_data("AAPL", start_date, end_date)
        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} days of AAPL data")
            print(f"  Date range: {data.index[0].date()} to {data.index[-1].date()}")
            print(f"  Columns: {list(data.columns)}")
            return True
        else:
            print("✗ No data returned (this might be due to network issues)")
            return False
    except Exception as e:
        print(f"✗ Error fetching data: {e}")
        return False

def test_normalize_to_day_of_year():
    """Test the day of year normalization function"""
    print("\nTesting day of year normalization...")
    
    # Create sample data
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    sample_data = pd.DataFrame({
        'Close': range(len(dates))
    }, index=dates)
    
    try:
        normalized = normalize_to_day_of_year(sample_data, 2023)
        
        if 'day_of_year' in normalized.columns and 'year' in normalized.columns:
            print("✓ Successfully normalized data to day of year")
            print(f"  First day of year: {normalized['day_of_year'].iloc[0]}")
            print(f"  Last day of year: {normalized['day_of_year'].iloc[-1]}")
            return True
        else:
            print("✗ Missing expected columns in normalized data")
            return False
    except Exception as e:
        print(f"✗ Error normalizing data: {e}")
        return False

def test_create_seasonal_chart():
    """Test the chart creation function"""
    print("\nTesting chart creation...")
    
    try:
        # Create sample data for multiple years
        years_data = {}
        for year in [2021, 2022, 2023]:
            dates = pd.date_range(f'{year}-01-01', f'{year}-12-31', freq='D')
            data = pd.DataFrame({
                'Close': [100 + i * 0.1 for i in range(len(dates))],
                'day_of_year': [d.dayofyear for d in dates],
                'year': year
            }, index=dates)
            years_data[year] = data
        
        fig_price = create_seasonal_price_chart("TEST", years_data)
        fig_normalized = create_normalized_seasonal_chart("TEST", years_data)
        fig_multi_year = create_multi_year_line_chart("TEST", years_data, 3)
        fig_recent = create_recent_comparison_chart("TEST", years_data)
        fig_rsi = create_rsi_chart("TEST", years_data)

        if fig_price is not None and fig_normalized is not None and fig_multi_year is not None and fig_recent is not None and fig_rsi is not None:
            print("✓ Successfully created seasonal charts")
            print(f"  Price chart title: {fig_price.layout.title.text}")
            print(f"  Normalized chart title: {fig_normalized.layout.title.text}")
            print(f"  Multi-year chart title: {fig_multi_year.layout.title.text}")
            print(f"  Recent chart title: {fig_recent.layout.title.text}")
            print(f"  RSI chart title: {fig_rsi.layout.title.text}")
            print(f"  Number of traces: {len(fig_price.data)}")

            # Test RSI calculation
            sample_data = years_data[list(years_data.keys())[0]]
            rsi_values = calculate_rsi(sample_data['Close'])
            print(f"  RSI calculation: {len(rsi_values)} values, range {rsi_values.min():.1f}-{rsi_values.max():.1f}")
            return True
        else:
            print("✗ Chart creation returned None")
            return False
    except Exception as e:
        print(f"✗ Error creating chart: {e}")
        return False

def main():
    """Run all tests"""
    print("Running Seasonal Stock Analysis App Tests")
    print("=" * 50)
    
    tests = [
        test_normalize_to_day_of_year,
        test_create_seasonal_chart,
        test_fetch_stock_data,  # Run this last as it depends on network
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! The app should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
