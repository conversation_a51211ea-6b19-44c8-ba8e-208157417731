import streamlit as st
import yfinance as yf
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime, timedelta
import numpy as np
import random
import os
import pickle
import hashlib



def get_cache_key(ticker, num_years, start_month, start_day, end_month, end_day):
    """Generate a unique cache key for the given parameters"""
    key_string = f"{ticker}_{num_years}_{start_month}_{start_day}_{end_month}_{end_day}"
    return hashlib.md5(key_string.encode()).hexdigest()

def get_multi_ticker_cache_key(tickers, num_years, start_month, start_day, end_month, end_day):
    """Generate a unique cache key for multi-ticker analysis"""
    # Sort tickers to ensure consistent key regardless of input order
    sorted_tickers = sorted(tickers)
    tickers_string = ",".join(sorted_tickers)
    key_string = f"MULTI_{tickers_string}_{num_years}_{start_month}_{start_day}_{end_month}_{end_day}"
    return hashlib.md5(key_string.encode()).hexdigest()

def get_cache_dir():
    """Get or create cache directory"""
    cache_dir = os.path.join(os.getcwd(), "stock_cache")
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    return cache_dir

def save_to_cache(ticker, num_years, start_month, start_day, end_month, end_day, years_data):
    """Save years_data to cache"""
    try:
        cache_key = get_cache_key(ticker, num_years, start_month, start_day, end_month, end_day)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        cache_data = {
            'type': 'single',
            'ticker': ticker,
            'num_years': num_years,
            'start_month': start_month,
            'start_day': start_day,
            'end_month': end_month,
            'end_day': end_day,
            'years_data': years_data,
            'timestamp': datetime.now()
        }

        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

        return True
    except Exception as e:
        return False

def save_multi_ticker_to_cache(tickers, num_years, start_month, start_day, end_month, end_day, all_years_data):
    """Save multi-ticker analysis data to cache"""
    try:
        cache_key = get_multi_ticker_cache_key(tickers, num_years, start_month, start_day, end_month, end_day)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        cache_data = {
            'type': 'multi',
            'tickers': sorted(tickers),  # Store sorted for consistency
            'num_years': num_years,
            'start_month': start_month,
            'start_day': start_day,
            'end_month': end_month,
            'end_day': end_day,
            'all_years_data': all_years_data,
            'timestamp': datetime.now()
        }

        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

        return True
    except Exception as e:
        return False

def load_from_cache(ticker, num_years, start_month, start_day, end_month, end_day):
    """Load years_data from cache"""
    try:
        cache_key = get_cache_key(ticker, num_years, start_month, start_day, end_month, end_day)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            return cache_data

        return None
    except Exception as e:
        return None

def load_multi_ticker_from_cache(tickers, num_years, start_month, start_day, end_month, end_day):
    """Load multi-ticker analysis data from cache"""
    try:
        cache_key = get_multi_ticker_cache_key(tickers, num_years, start_month, start_day, end_month, end_day)
        cache_dir = get_cache_dir()
        cache_file = os.path.join(cache_dir, f"{cache_key}.pkl")

        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            return cache_data

        return None
    except Exception as e:
        return None

def get_all_cached_entries():
    """Get all cached entries with metadata"""
    try:
        cache_dir = get_cache_dir()
        cached_entries = []

        for filename in os.listdir(cache_dir):
            if filename.endswith('.pkl'):
                try:
                    cache_file = os.path.join(cache_dir, filename)
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)

                    # Handle both single and multi-ticker cache entries
                    cache_type = cache_data.get('type', 'single')  # Default to single for backward compatibility

                    if cache_type == 'multi':
                        # Multi-ticker entry
                        entry = {
                            'type': 'multi',
                            'tickers': cache_data['tickers'],
                            'ticker_display': ', '.join(cache_data['tickers'][:3]) + ('...' if len(cache_data['tickers']) > 3 else ''),
                            'num_years': cache_data['num_years'],
                            'start_month': cache_data['start_month'],
                            'start_day': cache_data['start_day'],
                            'end_month': cache_data['end_month'],
                            'end_day': cache_data['end_day'],
                            'timestamp': cache_data['timestamp'],
                            'cache_key': filename[:-4]  # Remove .pkl extension
                        }
                    else:
                        # Single ticker entry
                        entry = {
                            'type': 'single',
                            'ticker': cache_data['ticker'],
                            'num_years': cache_data['num_years'],
                            'start_month': cache_data['start_month'],
                            'start_day': cache_data['start_day'],
                            'end_month': cache_data['end_month'],
                            'end_day': cache_data['end_day'],
                            'timestamp': cache_data['timestamp'],
                            'cache_key': filename[:-4]  # Remove .pkl extension
                        }

                    cached_entries.append(entry)
                except:
                    continue

        # Sort by timestamp (newest first)
        cached_entries.sort(key=lambda x: x['timestamp'], reverse=True)
        return cached_entries
    except Exception as e:
        return []

def clear_cache():
    """Clear all cached data"""
    try:
        cache_dir = get_cache_dir()
        cleared_count = 0

        for filename in os.listdir(cache_dir):
            if filename.endswith('.pkl'):
                os.remove(os.path.join(cache_dir, filename))
                cleared_count += 1

        return cleared_count
    except Exception as e:
        return 0

def normalize_canadian_ticker(ticker):
    """Normalize Canadian ticker symbols for different exchanges"""
    ticker = ticker.upper().strip()

    # Canadian exchange mappings
    canadian_exchanges = {
        'TSX': '.TO',      # Toronto Stock Exchange
        'TSXV': '.V',      # TSX Venture Exchange
        'CSE': '.CN',      # Canadian Securities Exchange
        'NEO': '.NE'       # NEO Exchange
    }

    # If ticker already has Canadian suffix, return as-is
    for suffix in canadian_exchanges.values():
        if ticker.endswith(suffix):
            return ticker

    # Common Canadian stocks that should use .TO by default
    major_canadian_stocks = {
        'SHOP', 'RY', 'TD', 'BNS', 'BMO', 'CM', 'CNR', 'CP', 'ENB', 'TRP',
        'SU', 'CNQ', 'IMO', 'CVE', 'WCP', 'WEED', 'ACB', 'HEXO', 'OGI',
        'BB', 'NTAR', 'LSPD', 'DOC', 'WELL', 'CTS', 'FOOD', 'TOI', 'REAL',
        'BAM', 'BIP', 'BEP', 'BBU', 'BIPC', 'BEPC', 'FFH', 'IFC', 'MFC',
        'SLF', 'GWO', 'POW', 'IAG', 'EMA', 'FTS', 'H', 'CU', 'AQN', 'BHC'
    }

    # If it's a known Canadian stock, add .TO suffix
    if ticker in major_canadian_stocks:
        return f"{ticker}.TO"

    # Otherwise return as-is (could be US stock or other)
    return ticker

def try_canadian_variations(base_ticker):
    """Generate Canadian ticker variations to try"""
    base_ticker = base_ticker.upper().strip()

    # Remove any existing suffix to get base ticker
    for suffix in ['.TO', '.V', '.CN', '.NE']:
        if base_ticker.endswith(suffix):
            base_ticker = base_ticker[:-len(suffix)]
            break

    variations = [
        base_ticker,           # Original ticker (US market)
        f"{base_ticker}.TO",   # TSX
        f"{base_ticker}.V",    # TSX Venture
        f"{base_ticker}.CN",   # Canadian Securities Exchange
        f"{base_ticker}.NE"    # NEO Exchange
    ]
    return variations

def fetch_stock_data_yahoo_direct(ticker, start_date, end_date):
    """Fetch stock data using Yahoo Finance direct API"""
    try:
        import requests
        import time

        # Convert dates to timestamps
        start_timestamp = int(start_date.timestamp())
        end_timestamp = int(end_date.timestamp())

        url = f"https://query1.finance.yahoo.com/v8/finance/chart/{ticker}"
        params = {
            "period1": start_timestamp,
            "period2": end_timestamp,
            "interval": "1d"
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        response = requests.get(url, params=params, headers=headers, timeout=15)
        data = response.json()

        if "chart" in data and data["chart"]["result"]:
            result = data["chart"]["result"][0]
            timestamps = result["timestamp"]
            quotes = result["indicators"]["quote"][0]

            # Convert to DataFrame
            dates = [datetime.fromtimestamp(ts) for ts in timestamps]
            df = pd.DataFrame({
                'Open': quotes.get('open', [None] * len(dates)),
                'High': quotes.get('high', [None] * len(dates)),
                'Low': quotes.get('low', [None] * len(dates)),
                'Close': quotes.get('close', [None] * len(dates)),
                'Volume': quotes.get('volume', [None] * len(dates))
            }, index=pd.DatetimeIndex(dates))

            # Remove rows with None values
            df = df.dropna()

            return df if not df.empty else None

    except Exception as e:
        return None

def fetch_stock_data_yfinance(ticker, start_date, end_date):
    """Fetch stock data using yfinance library (fallback)"""
    try:
        import time
        time.sleep(0.1)

        stock = yf.Ticker(ticker)
        data = stock.history(start=start_date, end=end_date, auto_adjust=True)

        return data if not data.empty else None

    except Exception as e:
        return None

def generate_demo_data(ticker, start_date, end_date):
    """Generate realistic demo stock data"""
    try:
        dates = pd.date_range(start=start_date, end=end_date, freq='D')
        dates = dates[dates.weekday < 5]  # Only weekdays

        if len(dates) == 0:
            return None

        # Generate realistic stock price movement
        np.random.seed(hash(ticker) % 1000)  # Consistent data for same ticker

        base_price = 100 + (hash(ticker) % 200)  # Base price between 100-300
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns

        # Add seasonal patterns
        for i, date in enumerate(dates):
            if date.month in [11, 12]:  # Holiday season boost
                returns[i] += 0.002
            elif date.month in [7, 8]:  # Summer slowdown
                returns[i] -= 0.001

        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        data = pd.DataFrame({
            'Open': [p * 0.99 for p in prices],
            'High': [p * 1.02 for p in prices],
            'Low': [p * 0.98 for p in prices],
            'Close': prices,
            'Volume': [random.randint(1000000, 10000000) for _ in prices]
        }, index=dates)

        return data

    except Exception as e:
        return None

def fetch_stock_data(ticker, start_date, end_date):
    """Fetch stock data with multiple fallback methods and Canadian exchange support"""

    # Method 1: Try normalized ticker first
    normalized_ticker = normalize_canadian_ticker(ticker)
    data = fetch_stock_data_yahoo_direct(normalized_ticker, start_date, end_date)
    if data is not None and not data.empty:
        return data

    # Method 2: Try all Canadian variations if normalized ticker failed
    if normalized_ticker == ticker:  # Only try variations if normalization didn't change anything
        variations = try_canadian_variations(ticker)
        for variation in variations:
            if variation != ticker:  # Skip the original ticker since we already tried it
                data = fetch_stock_data_yahoo_direct(variation, start_date, end_date)
                if data is not None and not data.empty:
                    return data

    # Method 3: yfinance library fallback with Canadian support
    data = fetch_stock_data_yfinance(normalized_ticker, start_date, end_date)
    if data is not None and not data.empty:
        return data

    # Method 4: Try yfinance with variations
    if normalized_ticker == ticker:
        variations = try_canadian_variations(ticker)
        for variation in variations:
            if variation != ticker:
                data = fetch_stock_data_yfinance(variation, start_date, end_date)
                if data is not None and not data.empty:
                    return data

    # Method 5: Demo data (last resort for testing)
    if ticker.upper() in ['DEMO', 'TEST']:
        return generate_demo_data(ticker, start_date, end_date)

    return None

def normalize_to_day_of_year(df, year):
    """Convert dates to day of year for seasonal comparison"""
    df_copy = df.copy()
    df_copy['day_of_year'] = df_copy.index.dayofyear
    df_copy['year'] = year
    return df_copy

def generate_distinct_colors(num_colors):
    """Generate very distinct colors for each line"""
    # Predefined distinct colors that are easily distinguishable
    distinct_colors = [
        '#1f77b4',  # Blue
        '#ff7f0e',  # Orange
        '#2ca02c',  # Green
        '#d62728',  # Red
        '#9467bd',  # Purple
        '#8c564b',  # Brown
        '#e377c2',  # Pink
        '#7f7f7f',  # Gray
        '#bcbd22',  # Olive
        '#17becf',  # Cyan
        '#ff9999',  # Light Red
        '#66b3ff',  # Light Blue
        '#99ff99',  # Light Green
        '#ffcc99',  # Light Orange
        '#ff99cc',  # Light Pink
        '#c2c2f0',  # Light Purple
        '#ffb3e6',  # Light Magenta
        '#c4e17f',  # Light Lime
        '#76d7c4',  # Light Teal
        '#f7dc6f'   # Light Yellow
    ]

    # Return the required number of colors, cycling if needed
    return [distinct_colors[i % len(distinct_colors)] for i in range(num_colors)]

def create_seasonal_price_chart(ticker, years_data, use_log_scale=False, show_only_average=False):
    """Create overlaid seasonal price chart with distinct colors and average band"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Calculate average line data by month
    all_month_data = {}  # month -> list of prices

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            for idx, row in data.iterrows():
                # Get month from the index (date)
                month = idx.month
                price = row['Close']
                if month not in all_month_data:
                    all_month_data[month] = []
                all_month_data[month].append(price)

    # Calculate average prices for each month
    avg_months = sorted(all_month_data.keys())
    avg_prices = [np.mean(all_month_data[month]) for month in avg_months]

    # Convert months to day-of-year for plotting (use middle of month)
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average line over the entire period using monthly averages
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        # Create a smooth interpolation over the full year range
        f = interp1d(avg_days, avg_prices, kind='cubic', fill_value='extrapolate')
        # Create smooth line covering the entire period
        smooth_days = np.linspace(1, 365, 365)  # One point per day
        smooth_prices = f(smooth_days)
    else:
        smooth_days = avg_days
        smooth_prices = avg_prices

    # Add average band (semi-transparent and smooth)
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_prices,
        mode='lines',
        name='Average',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        fill=None,
        hovertemplate='<b>Average</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Avg Price: $%{y:.2f}<br>' +
                     '<extra></extra>'
    ))

    # Add individual year lines (only if not showing average only)
    if not show_only_average:
        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=data['Close'],
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Price: $%{y:.2f}<br>' +
                                 '<extra></extra>'
                ))

    y_axis_type = 'log' if use_log_scale else 'linear'
    y_axis_title = f'Stock Price ($) - {"Log Scale" if use_log_scale else "Linear Scale"}'

    fig.update_layout(
        title=f'Seasonal Price Patterns for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title=y_axis_title,
        yaxis_type=y_axis_type,
        hovermode='closest',  # Changed from 'x unified' to show only hovered line
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    return fig

def create_multi_year_line_chart(ticker, years_data, num_years_to_show=5):
    """Create a continuous line chart showing multiple years of price data"""
    fig = go.Figure()

    # Sort years (newest to oldest) and take the requested number
    sorted_years = sorted(years_data.keys(), reverse=True)[:num_years_to_show]
    sorted_years = sorted(sorted_years)  # Re-sort oldest to newest for display

    # Combine all data into a continuous timeline
    all_dates = []
    all_prices = []

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Use actual dates instead of day_of_year for continuous timeline
            all_dates.extend(data.index.tolist())
            all_prices.extend(data['Close'].tolist())

    if all_dates and all_prices:
        # Sort by date to ensure proper timeline
        combined_data = list(zip(all_dates, all_prices))
        combined_data.sort(key=lambda x: x[0])
        all_dates, all_prices = zip(*combined_data)

        # Create single continuous line
        fig.add_trace(go.Scatter(
            x=all_dates,
            y=all_prices,
            mode='lines',
            name=f'{ticker} Price',
            line=dict(color='#1f77b4', width=2),
            hovertemplate='<b>%{x}</b><br>' +
                         'Price: $%{y:.2f}<br>' +
                         '<extra></extra>'
        ))

    fig.update_layout(
        title=f'{ticker} Price History - Last {len(sorted_years)} Years',
        xaxis_title='Date',
        yaxis_title='Stock Price ($)',
        hovermode='closest',
        height=600,
        showlegend=False  # Single line doesn't need legend
    )

    # Format x-axis to show dates nicely
    fig.update_xaxes(
        tickformat='%Y-%m',
        dtick='M3'  # Show every 3 months
    )

    return fig

def create_recent_comparison_chart(ticker, years_data):
    """Create comparison chart for last year vs this year only"""
    fig = go.Figure()

    # Get current year and last year
    current_year = datetime.now().year
    last_year = current_year - 1

    # Colors for the two years
    colors = ['#d62728', '#1f77b4']  # Red for last year, Blue for this year
    year_labels = [f'{last_year} (Last Year)', f'{current_year} (This Year)']

    years_to_show = [last_year, current_year]

    for i, year in enumerate(years_to_show):
        if year in years_data:
            data = years_data[year]
            if data is not None and not data.empty:
                # Calculate cumulative returns from start of period
                start_price = data['Close'].iloc[0]
                returns = ((data['Close'] / start_price) - 1) * 100

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=returns,
                    mode='lines',
                    name=year_labels[i],
                    line=dict(color=colors[i], width=3),  # Thicker lines for emphasis
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Return: %{y:.2f}%<br>' +
                                 '<extra></extra>'
                ))

    fig.update_layout(
        title=f'Recent Year Comparison for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title='Cumulative Return (%)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=500
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add horizontal line at 0%
    fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)

    return fig

def create_normalized_seasonal_chart(ticker, years_data, show_only_average=False):
    """Create seasonal chart with all prices normalized to start at 100"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Calculate normalized average data by month
    all_month_normalized = {}  # month -> list of normalized prices

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Normalize to start at 100
            start_price = data['Close'].iloc[0]
            normalized_prices = (data['Close'] / start_price) * 100

            for idx, norm_price in enumerate(normalized_prices):
                # Get month from the index (date)
                month = data.index[idx].month
                if month not in all_month_normalized:
                    all_month_normalized[month] = []
                all_month_normalized[month].append(norm_price)

    # Calculate average normalized prices and standard deviations for each month
    avg_months = sorted(all_month_normalized.keys())
    avg_normalized = [np.mean(all_month_normalized[month]) for month in avg_months]
    std_normalized = [np.std(all_month_normalized[month]) for month in avg_months]

    # Convert months to day-of-year for plotting (use middle of month)
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average and std dev lines over the entire period using monthly data
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        # Create smooth interpolations over the full year range
        f_avg = interp1d(avg_days, avg_normalized, kind='cubic', fill_value='extrapolate')
        f_std = interp1d(avg_days, std_normalized, kind='cubic', fill_value='extrapolate')
        # Create smooth lines covering the entire period
        smooth_days = np.linspace(1, 365, 365)  # One point per day
        smooth_normalized = f_avg(smooth_days)
        smooth_std_norm = f_std(smooth_days)
    else:
        smooth_days = avg_days
        smooth_normalized = avg_normalized
        smooth_std_norm = std_normalized

    # Add standard deviation bands (upper and lower)
    upper_band = smooth_normalized + smooth_std_norm
    lower_band = smooth_normalized - smooth_std_norm

    # Add upper std dev band (invisible line for fill)
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=upper_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),  # Invisible line
        showlegend=False,
        hoverinfo='skip'
    ))

    # Add lower std dev band with fill to upper band
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=lower_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),  # Invisible line
        fill='tonexty',  # Fill to previous trace (upper band)
        fillcolor='rgba(128, 128, 128, 0.2)',  # Semi-transparent gray
        name='±1 Std Dev',
        hovertemplate='<b>Standard Deviation Band</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Lower: %{y:.1f}<br>' +
                     '<extra></extra>'
    ))

    # Add average line
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_normalized,
        mode='lines',
        name='Average',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        fill=None,
        hovertemplate='<b>Average</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Normalized: %{y:.1f}<br>' +
                     'Std Dev: %{customdata:.1f}<br>' +
                     '<extra></extra>',
        customdata=smooth_std_norm
    ))

    # Add individual year lines (only if not showing average only)
    if not show_only_average:
        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                # Normalize to start at 100
                start_price = data['Close'].iloc[0]
                normalized_prices = (data['Close'] / start_price) * 100

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=normalized_prices,
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'Normalized: %{y:.1f}<br>' +
                                 '<extra></extra>'
                ))

    fig.update_layout(
        title=f'Normalized Seasonal Patterns for {ticker} (Starting at 100)',
        xaxis_title='Day of Year',
        yaxis_title='Normalized Price (Start = 100)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    # Add horizontal line at 100 (starting point)
    fig.add_hline(y=100, line_dash="dash", line_color="gray", opacity=0.5)

    return fig

def calculate_rsi(prices, period=14):
    """Calculate Relative Strength Index (RSI)"""
    if len(prices) < period + 1:
        return pd.Series([50] * len(prices), index=prices.index)

    # Calculate price changes
    delta = prices.diff()

    # Separate gains and losses
    gains = delta.where(delta > 0, 0)
    losses = -delta.where(delta < 0, 0)

    # Calculate average gains and losses
    avg_gains = gains.rolling(window=period).mean()
    avg_losses = losses.rolling(window=period).mean()

    # Calculate RS and RSI
    rs = avg_gains / avg_losses
    rsi = 100 - (100 / (1 + rs))

    # Fill NaN values with 50 (neutral)
    rsi = rsi.fillna(50)

    return rsi

def create_rsi_chart(ticker, years_data, show_only_average=False):
    """Create RSI chart showing seasonal RSI patterns"""
    fig = go.Figure()

    # Sort years (oldest to newest)
    sorted_years = sorted(years_data.keys())
    num_years = len(sorted_years)

    # Generate distinct colors for each year
    colors = generate_distinct_colors(num_years)

    # Calculate RSI for each year and collect monthly averages
    all_month_rsi = {}  # month -> list of RSI values

    for year in sorted_years:
        data = years_data[year]
        if data is not None and not data.empty:
            # Calculate RSI for this year
            rsi_values = calculate_rsi(data['Close'])

            # Add RSI column to data
            data_with_rsi = data.copy()
            data_with_rsi['RSI'] = rsi_values

            # Collect monthly RSI data
            for idx, row in data_with_rsi.iterrows():
                month = idx.month
                rsi_val = row['RSI']
                if month not in all_month_rsi:
                    all_month_rsi[month] = []
                all_month_rsi[month].append(rsi_val)

    # Calculate average RSI and standard deviation for each month
    avg_months = sorted(all_month_rsi.keys())
    avg_rsi = [np.mean(all_month_rsi[month]) for month in avg_months]
    std_rsi = [np.std(all_month_rsi[month]) for month in avg_months]

    # Convert months to day-of-year for plotting
    month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                    7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
    avg_days = [month_to_day[month] for month in avg_months]

    # Smooth the average RSI line
    if len(avg_days) > 3:
        from scipy.interpolate import interp1d
        f_avg = interp1d(avg_days, avg_rsi, kind='cubic', fill_value='extrapolate')
        f_std = interp1d(avg_days, std_rsi, kind='cubic', fill_value='extrapolate')
        smooth_days = np.linspace(1, 365, 365)
        smooth_rsi = f_avg(smooth_days)
        smooth_std_rsi = f_std(smooth_days)
    else:
        smooth_days = avg_days
        smooth_rsi = avg_rsi
        smooth_std_rsi = std_rsi

    # Add standard deviation bands
    upper_band = smooth_rsi + smooth_std_rsi
    lower_band = smooth_rsi - smooth_std_rsi

    # Clip bands to RSI range (0-100)
    upper_band = np.clip(upper_band, 0, 100)
    lower_band = np.clip(lower_band, 0, 100)

    # Add upper std dev band (invisible line for fill)
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=upper_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),
        showlegend=False,
        hoverinfo='skip'
    ))

    # Add lower std dev band with fill
    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=lower_band,
        mode='lines',
        line=dict(color='rgba(128, 128, 128, 0)'),
        fill='tonexty',
        fillcolor='rgba(128, 128, 128, 0.2)',
        name='±1 Std Dev',
        hovertemplate='<b>RSI Standard Deviation Band</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'Lower RSI: %{y:.1f}<br>' +
                     '<extra></extra>'
    ))

    # Add average RSI line
    avg_color = 'rgba(128, 128, 128, 0.8)' if not show_only_average else 'rgba(31, 119, 180, 1.0)'
    avg_width = 4 if not show_only_average else 3

    fig.add_trace(go.Scatter(
        x=smooth_days,
        y=smooth_rsi,
        mode='lines',
        name='Average RSI',
        line=dict(color=avg_color, width=avg_width, shape='spline'),
        hovertemplate='<b>Average RSI</b><br>' +
                     'Day of Year: %{x:.0f}<br>' +
                     'RSI: %{y:.1f}<br>' +
                     'Std Dev: %{customdata:.1f}<br>' +
                     '<extra></extra>',
        customdata=smooth_std_rsi
    ))

    # Add individual year RSI lines (only if not showing average only)
    if not show_only_average:
        for i, year in enumerate(sorted_years):
            data = years_data[year]
            if data is not None and not data.empty:
                rsi_values = calculate_rsi(data['Close'])

                fig.add_trace(go.Scatter(
                    x=data['day_of_year'],
                    y=rsi_values,
                    mode='lines',
                    name=f'{year}',
                    line=dict(color=colors[i], width=2),
                    hovertemplate=f'<b>{year}</b><br>' +
                                 'Day of Year: %{x}<br>' +
                                 'RSI: %{y:.1f}<br>' +
                                 '<extra></extra>'
                ))

    # Add RSI reference lines
    fig.add_hline(y=70, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Overbought (70)", annotation_position="bottom right")
    fig.add_hline(y=30, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Oversold (30)", annotation_position="top right")
    fig.add_hline(y=50, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Neutral (50)", annotation_position="top left")

    fig.update_layout(
        title=f'Seasonal RSI Patterns for {ticker}',
        xaxis_title='Day of Year',
        yaxis_title='RSI (Relative Strength Index)',
        yaxis=dict(range=[0, 100]),
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=500
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    return fig

def get_ticker_specific_events(ticker, year):
    """Get ticker-specific events and volatility drivers for a given year"""

    # Define ticker-specific events
    ticker_events = {
        'AAPL': {
            2024: "EARNINGS: Q4 revenue $94.9B (+6% YoY), iPhone revenue $46.2B (+6%), Services $25B (+12%)\nPRODUCT LAUNCHES: iPhone 16 Pro with A18 Pro chip (Sep), Apple Intelligence features rollout\nMANAGEMENT COMMENTARY: 'iPhone 16 designed for Apple Intelligence from ground up' - Tim Cook\nKEY METRICS: China revenue $15B (-0.3%), Wearables $9B (+1%), Mac $7.7B (+2%)\nOUTLOOK: Services growth acceleration expected, AI features driving upgrade cycle",
            2023: "EARNINGS: Q4 revenue $89.5B (-1% YoY), iPhone revenue $43.8B (+3%), Services $22.3B (+16%)\nPRODUCT LAUNCHES: iPhone 15 with USB-C (Sep), Vision Pro unveiled at WWDC (Jun)\nMANAGEMENT COMMENTARY: 'Vision Pro represents entirely new category' - Tim Cook\nKEY METRICS: China revenue $15.1B (-2.5%), Active devices 2.2B, Services attach rate 31%\nOUTLOOK: Vision Pro launch 2024, Services momentum continues, China stabilization",
            2022: "• iPhone 14 launch (Sep)\n• Q4 earnings miss (Oct)\n• Supply chain recovery\n• Services growth 14%",
            2021: "• iPhone 13 launch (Sep)\n• Q1 record $123B revenue\n• M1 MacBook Pro launch\n• Stock split 4:1 (Aug)",
            2020: "• iPhone 12 launch delayed (Oct)\n• Q1 record $111B revenue\n• Work-from-home Mac surge\n• Stock split 4:1 (Aug)",
            2019: "• iPhone 11 launch (Sep)\n• Q4 earnings beat (Oct)\n• Services revenue $46B\n• AirPods Pro launch",
            2018: "• iPhone XS/XR launch (Sep)\n• Q4 revenue guidance cut\n• China sales decline\n• Services growth slows",
            2017: "• iPhone X launch (Nov)\n• Q1 record $88B revenue\n• $1T market cap approach\n• Strong iPhone upgrade cycle",
            2016: "• iPhone 7 launch (Sep)\n• First annual revenue decline\n• Q4 earnings miss\n• Services growth 24%",
            2015: "• iPhone 6s launch (Sep)\n• Apple Watch debut (Apr)\n• Q1 record $75B revenue\n• China revenue doubles"
        },
        'MSFT': {
            2024: "EARNINGS: Q1 revenue $65.6B (+16% YoY), Azure growth 33%, Productivity revenue $28.3B (+12%)\nPRODUCT LAUNCHES: Copilot Pro subscription, Azure AI services expansion, Gaming revenue surge\nMANAGEMENT COMMENTARY: 'Copilot is becoming the UI for AI' - Satya Nadella\nKEY METRICS: Microsoft 365 Commercial seats 440M, Teams Phone 15M seats, Gaming revenue $7.1B (+61%)\nOUTLOOK: AI monetization accelerating, Azure growth sustainable, Copilot adoption expanding",
            2023: "• OpenAI partnership (Jan)\n• Q4 Azure growth 26%\n• Copilot launch (Mar)\n• Activision deal approved",
            2022: "• Q4 Azure growth 40%\n• Teams 270M users\n• Activision acquisition (Jan)\n• Cloud revenue $91B",
            2021: "• Q4 Azure growth 51%\n• Teams 250M users\n• Xbox Series X launch\n• Cloud revenue $60B",
            2020: "• Q4 Azure growth 62%\n• Teams 115M daily users\n• Surface revenue up 37%\n• Work-from-home boost",
            2019: "• Q4 Azure growth 64%\n• Office 365 200M users\n• Surface revenue up 21%\n• LinkedIn revenue $6.8B",
            2018: "• Q4 Azure growth 76%\n• Office 365 155M users\n• LinkedIn acquisition complete\n• Cloud revenue $32B",
            2017: "• Q4 Azure growth 93%\n• Office 365 100M users\n• Surface revenue up 32%\n• Cloud revenue $20B",
            2016: "• Q4 Azure growth 120%\n• Office 365 70M users\n• Surface revenue up 29%\n• Cloud transition",
            2015: "• Windows 10 launch (Jul)\n• Office 365 15M users\n• Azure revenue up 140%\n• Nadella's vision"
        },
        'GOOGL': {
            2024: "• Q3 Search revenue $49B\n• Gemini AI integration\n• Cloud revenue $11B (+35%)\n• YouTube Shorts 70B hours",
            2023: "• Q4 Search revenue $48B\n• Bard AI launch (Mar)\n• Cloud revenue $33B (+26%)\n• YouTube revenue $31B",
            2022: "• Q4 Search revenue $42B\n• YouTube revenue decline\n• Cloud revenue $26B (+32%)\n• Pixel 7 launch",
            2021: "• Q4 Search revenue $43B\n• YouTube revenue $28B\n• Cloud revenue $19B (+47%)\n• Pixel 6 launch",
            2020: "• Q4 Search revenue $32B\n• YouTube revenue $20B\n• Cloud revenue $13B (+47%)\n• Pixel 5 launch",
            2019: "• Q4 Search revenue $27B\n• YouTube revenue $15B\n• Cloud revenue $9B (+53%)\n• Pixel 4 launch",
            2018: "• Q4 Search revenue $24B\n• YouTube revenue $11B\n• Cloud revenue $6B (+76%)\n• Pixel 3 launch",
            2017: "• Q4 Search revenue $22B\n• YouTube revenue $8B\n• Cloud revenue $4B (+85%)\n• Pixel 2 launch",
            2016: "• Q4 Search revenue $19B\n• YouTube revenue $6B\n• Cloud revenue $2B\n• Pixel phone launch",
            2015: "• Q4 Search revenue $16B\n• Alphabet restructuring (Aug)\n• YouTube Red launch\n• Cloud platform launch"
        },
        'TSLA': {
            2024: "• Q3 deliveries 463K (+6%)\n• Cybertruck production ramp\n• FSD v12 release\n• Energy storage 20.3 GWh",
            2023: "• Q4 deliveries 484K (+20%)\n• Price cuts throughout year\n• Cybertruck delivery event\n• FSD beta 11.4 release",
            2022: "• Q4 deliveries 405K (+31%)\n• Shanghai factory shutdown\n• Austin/Berlin ramp\n• Stock split 3:1 (Aug)",
            2021: "• Q4 deliveries 308K (+87%)\n• S&P 500 inclusion (Dec)\n• Model S Plaid launch\n• FSD beta rollout",
            2020: "• Q4 deliveries 180K (+61%)\n• S&P 500 inclusion announced\n• Battery Day (Sep)\n• Stock split 5:1 (Aug)",
            2019: "• Q4 deliveries 112K (+23%)\n• Shanghai factory opening\n• Cybertruck unveiling (Nov)\n• Model Y unveiling",
            2018: "• Q4 deliveries 90K (+83%)\n• Model 3 production ramp\n• SEC settlement $20M\n• Going private tweet (Aug)",
            2017: "• Q4 deliveries 29K (+27%)\n• Model 3 production start\n• SolarCity merger complete\n• Gigafactory 1 opening",
            2016: "• Q4 deliveries 22K (+73%)\n• Model 3 unveiling (Mar)\n• SolarCity acquisition\n• Autopilot 2.0 launch",
            2015: "• Q4 deliveries 17K (+75%)\n• Model X launch (Sep)\n• Gigafactory construction\n• Autopilot 1.0 launch"
        },
        'AMZN': {
            2024: "• Q3 AWS revenue $27B (+19%)\n• Prime Video ads launch\n• Q3 net income $15B\n• One Medical integration",
            2023: "• Q4 AWS revenue $24B (+13%)\n• Prime membership price hike\n• Alexa layoffs 18K\n• MGM acquisition complete",
            2022: "• Q4 AWS revenue $21B (+20%)\n• First annual loss since 2014\n• Prime price increase\n• Rivian investment loss",
            2021: "• Q4 AWS revenue $17B (+40%)\n• Prime Day record $11B\n• Bezos steps down as CEO\n• MGM acquisition $8.5B",
            2020: "• Q4 AWS revenue $13B (+28%)\n• Prime membership 200M\n• Pandemic e-commerce surge\n• Bezos sells $10B stock",
            2019: "• Q4 AWS revenue $10B (+34%)\n• One-day shipping rollout\n• HQ2 split decision\n• Advertising revenue $14B",
            2018: "• Q4 AWS revenue $7B (+45%)\n• Prime membership 100M\n• HQ2 search concludes\n• Advertising revenue $10B",
            2017: "• Q4 AWS revenue $5B (+43%)\n• Whole Foods acquisition $13.7B\n• Alexa Echo Show launch\n• Prime membership 90M",
            2016: "• Q4 AWS revenue $3.5B (+47%)\n• Prime Video global launch\n• Echo Dot launch\n• Prime membership 65M",
            2015: "• Q4 AWS revenue $2.4B (+69%)\n• Prime membership 54M\n• Echo launch\n• Fire Phone discontinued"
        }
    }

    # Get ticker-specific events or fall back to generic
    if ticker.upper() in ticker_events and year in ticker_events[ticker.upper()]:
        return ticker_events[ticker.upper()][year]
    else:
        # Generic company-focused events as fallback
        generic_events = {
            2024: "• Q3/Q4 earnings reports\n• Product launches/updates\n• Management changes\n• Strategic initiatives",
            2023: "• Annual earnings results\n• New product releases\n• Business expansion\n• Operational changes",
            2022: "• Quarterly earnings\n• Product development\n• Market expansion\n• Cost management",
            2021: "• Earnings growth\n• Product innovation\n• Market share gains\n• Digital transformation",
            2020: "• Pandemic response\n• Business adaptation\n• Remote operations\n• Strategic pivots",
            2019: "• Revenue growth\n• Product launches\n• Market expansion\n• Operational efficiency",
            2018: "• Earnings performance\n• Product development\n• Strategic investments\n• Market positioning"
        }
        return generic_events.get(year, "• Quarterly earnings reports\n• Product developments\n• Business initiatives\n• Operational updates")

def get_company_info_from_api(ticker):
    """Get company information from yfinance API with Canadian exchange support"""
    try:
        import yfinance as yf

        # Try normalized ticker first
        normalized_ticker = normalize_canadian_ticker(ticker)
        stock = yf.Ticker(normalized_ticker)
        info = stock.info

        if info and 'longName' in info:
            return {
                'name': info.get('longName', f'{ticker} Corporation'),
                'industry': info.get('industry', 'Unknown Industry'),
                'sector': info.get('sector', 'Unknown Sector'),
                'description': info.get('longBusinessSummary', f'{ticker} is a publicly traded company.'),
                'market_cap': f"${info.get('marketCap', 0) / 1e12:.1f}T" if info.get('marketCap', 0) > 1e12 else f"${info.get('marketCap', 0) / 1e9:.1f}B" if info.get('marketCap', 0) > 1e9 else 'N/A',
                'employees': f"{info.get('fullTimeEmployees', 0):,}" if info.get('fullTimeEmployees') else 'N/A',
                'founded': 'N/A',  # Not available in yfinance
                'headquarters': f"{info.get('city', '')}, {info.get('state', '')}" if info.get('city') else 'N/A',
                'key_products': info.get('longBusinessSummary', 'Various products and services')[:100] + '...' if info.get('longBusinessSummary') else 'Various products and services',
                'analyst_predictions': {
                    'price_target': f"${info.get('targetMeanPrice', 0):.2f}" if info.get('targetMeanPrice') else 'Not available',
                    'rating': info.get('recommendationKey', 'Not available').title(),
                    'growth_outlook': f"Revenue growth expected based on analyst estimates",
                    'key_catalysts': 'Business developments and market expansion',
                    'risks': 'Market volatility and industry-specific risks'
                }
            }

        # If normalized ticker failed, try variations
        if normalized_ticker == ticker:
            variations = try_canadian_variations(ticker)
            for variation in variations:
                if variation != ticker:
                    try:
                        stock = yf.Ticker(variation)
                        info = stock.info
                        if info and 'longName' in info:
                            return {
                                'name': info.get('longName', f'{ticker} Corporation'),
                                'industry': info.get('industry', 'Unknown Industry'),
                                'sector': info.get('sector', 'Unknown Sector'),
                                'description': info.get('longBusinessSummary', f'{ticker} is a publicly traded company.'),
                                'market_cap': f"${info.get('marketCap', 0) / 1e12:.1f}T" if info.get('marketCap', 0) > 1e12 else f"${info.get('marketCap', 0) / 1e9:.1f}B" if info.get('marketCap', 0) > 1e9 else 'N/A',
                                'employees': f"{info.get('fullTimeEmployees', 0):,}" if info.get('fullTimeEmployees') else 'N/A',
                                'founded': 'N/A',
                                'headquarters': f"{info.get('city', '')}, {info.get('state', '')}" if info.get('city') else 'N/A',
                                'key_products': info.get('longBusinessSummary', 'Various products and services')[:100] + '...' if info.get('longBusinessSummary') else 'Various products and services',
                                'analyst_predictions': {
                                    'price_target': f"${info.get('targetMeanPrice', 0):.2f}" if info.get('targetMeanPrice') else 'Not available',
                                    'rating': info.get('recommendationKey', 'Not available').title(),
                                    'growth_outlook': f"Revenue growth expected based on analyst estimates",
                                    'key_catalysts': 'Business developments and market expansion',
                                    'risks': 'Market volatility and industry-specific risks'
                                }
                            }
                    except:
                        continue

    except Exception as e:
        pass

    return None

def get_company_info(ticker):
    """Get comprehensive company information including analyst predictions"""

    # First try to get real data from API
    api_info = get_company_info_from_api(ticker)
    if api_info:
        return api_info


    # Fallback to static data for major tickers
    company_data = {
        'AAPL': {
            'name': 'Apple Inc.',
            'industry': 'Consumer Electronics & Technology',
            'sector': 'Technology',
            'description': 'Apple designs, manufactures, and markets smartphones, personal computers, tablets, wearables, and accessories. The company also sells various related services including digital content and cloud services. Apple is known for its iPhone, Mac, iPad, Apple Watch, and AirPods products, along with services like the App Store, Apple Music, and iCloud.',
            'market_cap': '$3.0T',
            'employees': '161,000',
            'founded': '1976',
            'headquarters': 'Cupertino, California',
            'key_products': 'iPhone (52% of revenue), Services (22%), Mac (10%), iPad (8%), Wearables (8%)',
            'analyst_predictions': {
                'price_target': '$220 (avg of 45 analysts)',
                'rating': 'Buy (28 Buy, 15 Hold, 2 Sell)',
                'growth_outlook': 'iPhone 16 cycle expected to drive 5-7% revenue growth in FY2024',
                'key_catalysts': 'Apple Intelligence rollout, Vision Pro expansion, Services growth',
                'risks': 'China market volatility, regulatory pressures, hardware saturation'
            }
        },
        'MSFT': {
            'name': 'Microsoft Corporation',
            'industry': 'Software & Cloud Computing',
            'sector': 'Technology',
            'description': 'Microsoft develops, licenses, and supports software, services, devices, and solutions worldwide. The company operates through three segments: Productivity and Business Processes (Office 365, Teams), Intelligent Cloud (Azure, Windows Server), and More Personal Computing (Windows, Xbox, Surface).',
            'market_cap': '$2.8T',
            'employees': '221,000',
            'founded': '1975',
            'headquarters': 'Redmond, Washington',
            'key_products': 'Azure (40% growth), Office 365 (440M seats), Windows, Xbox, Surface',
            'analyst_predictions': {
                'price_target': '$450 (avg of 42 analysts)',
                'rating': 'Strong Buy (35 Buy, 7 Hold, 0 Sell)',
                'growth_outlook': 'AI-driven growth expected to accelerate cloud revenue 25-30%',
                'key_catalysts': 'Copilot monetization, Azure AI services, OpenAI partnership',
                'risks': 'Cloud competition, AI investment costs, regulatory scrutiny'
            }
        },
        'GOOGL': {
            'name': 'Alphabet Inc. (Google)',
            'industry': 'Internet & Digital Advertising',
            'sector': 'Communication Services',
            'description': 'Alphabet is a holding company that gives ambitious projects the resources, freedom, and focus to make their ideas happen. Google, Alphabet\'s largest subsidiary, is a global technology leader focused on improving the ways people connect with information through search, advertising, cloud computing, and AI.',
            'market_cap': '$2.1T',
            'employees': '182,000',
            'founded': '1998',
            'headquarters': 'Mountain View, California',
            'key_products': 'Search (57% of revenue), YouTube (11%), Cloud (11%), Other Bets (1%)',
            'analyst_predictions': {
                'price_target': '$175 (avg of 40 analysts)',
                'rating': 'Buy (30 Buy, 9 Hold, 1 Sell)',
                'growth_outlook': 'Search AI integration and cloud growth driving 8-12% revenue growth',
                'key_catalysts': 'Gemini AI rollout, YouTube Shorts monetization, Cloud expansion',
                'risks': 'Antitrust regulations, AI competition, search disruption'
            }
        },
        'TSLA': {
            'name': 'Tesla, Inc.',
            'industry': 'Electric Vehicles & Clean Energy',
            'sector': 'Consumer Discretionary',
            'description': 'Tesla designs, develops, manufactures, and sells electric vehicles, energy generation and storage systems. The company operates through two segments: automotive (electric vehicles, regulatory credits) and energy generation and storage (solar panels, energy storage systems).',
            'market_cap': '$800B',
            'employees': '140,000',
            'founded': '2003',
            'headquarters': 'Austin, Texas',
            'key_products': 'Model 3/Y (85% of deliveries), Cybertruck, Energy Storage, FSD',
            'analyst_predictions': {
                'price_target': '$275 (avg of 35 analysts)',
                'rating': 'Hold (15 Buy, 15 Hold, 5 Sell)',
                'growth_outlook': 'Delivery growth of 15-25% expected with Cybertruck ramp',
                'key_catalysts': 'Cybertruck production, FSD breakthrough, energy business growth',
                'risks': 'EV competition, production challenges, regulatory changes'
            }
        },
        'AMZN': {
            'name': 'Amazon.com, Inc.',
            'industry': 'E-commerce & Cloud Computing',
            'sector': 'Consumer Discretionary',
            'description': 'Amazon is a multinational technology company focusing on e-commerce, cloud computing, digital streaming, and artificial intelligence. It operates through three segments: North America, International, and Amazon Web Services (AWS). The company is known for its disruption of well-established industries.',
            'market_cap': '$1.5T',
            'employees': '1,541,000',
            'founded': '1994',
            'headquarters': 'Seattle, Washington',
            'key_products': 'AWS (70% of operating income), Prime (200M+ members), Advertising, Logistics',
            'analyst_predictions': {
                'price_target': '$200 (avg of 38 analysts)',
                'rating': 'Buy (28 Buy, 9 Hold, 1 Sell)',
                'growth_outlook': 'AWS reacceleration and advertising growth driving 10-15% revenue growth',
                'key_catalysts': 'AWS AI services, Prime Video ads, logistics optimization',
                'risks': 'E-commerce saturation, cloud competition, regulatory pressures'
            }
        },
        'SHOP': {
            'name': 'Shopify Inc.',
            'industry': 'E-commerce Software',
            'sector': 'Technology',
            'description': 'Shopify provides a commerce platform and services in Canada, the United States, the United Kingdom, Australia, Latin America, and internationally. The company offers Shopify Plus, a commerce platform for enterprise brands; and Shopify Point of Sale, a cloud-based point-of-sale solution.',
            'market_cap': '$75B',
            'employees': '12,000',
            'founded': '2006',
            'headquarters': 'Ottawa, Ontario, Canada',
            'key_products': 'Shopify Platform, Shopify Plus, Shopify POS, Shopify Payments',
            'analyst_predictions': {
                'price_target': '$85 (avg of 25 analysts)',
                'rating': 'Buy (18 Buy, 6 Hold, 1 Sell)',
                'growth_outlook': 'E-commerce growth and international expansion driving 20-25% revenue growth',
                'key_catalysts': 'AI-powered features, B2B expansion, international markets',
                'risks': 'E-commerce competition, economic slowdown, merchant churn'
            }
        },
        'RY': {
            'name': 'Royal Bank of Canada',
            'industry': 'Banking & Financial Services',
            'sector': 'Financial Services',
            'description': 'Royal Bank of Canada operates as a diversified financial service company worldwide. The company provides personal and commercial banking, wealth management services, insurance, investor services, and capital markets products and services.',
            'market_cap': '$180B',
            'employees': '89,000',
            'founded': '1869',
            'headquarters': 'Toronto, Ontario, Canada',
            'key_products': 'Personal Banking, Commercial Banking, Wealth Management, Capital Markets',
            'analyst_predictions': {
                'price_target': '$145 (avg of 20 analysts)',
                'rating': 'Buy (15 Buy, 4 Hold, 1 Sell)',
                'growth_outlook': 'Steady dividend growth and market expansion driving 8-12% returns',
                'key_catalysts': 'Interest rate environment, US expansion, digital transformation',
                'risks': 'Credit losses, regulatory changes, economic downturn'
            }
        },
        'CNR': {
            'name': 'Canadian National Railway Company',
            'industry': 'Transportation & Logistics',
            'sector': 'Industrials',
            'description': 'Canadian National Railway Company, together with its subsidiaries, engages in the rail and related transportation business. The company transports cargo serving exporters, importers, retailers, farmers, and manufacturers.',
            'market_cap': '$85B',
            'employees': '24,000',
            'founded': '1919',
            'headquarters': 'Montreal, Quebec, Canada',
            'key_products': 'Rail Transportation, Intermodal Services, Automotive Transport, Forest Products',
            'analyst_predictions': {
                'price_target': '$165 (avg of 18 analysts)',
                'rating': 'Buy (12 Buy, 5 Hold, 1 Sell)',
                'growth_outlook': 'North American trade growth and efficiency improvements driving 10-15% growth',
                'key_catalysts': 'Trade volume recovery, operational efficiency, infrastructure investments',
                'risks': 'Economic slowdown, regulatory changes, weather disruptions'
            }
        }
    }

    # Return company data or generic info for unknown tickers
    if ticker.upper() in company_data:
        return company_data[ticker.upper()]
    else:
        return {
            'name': f'{ticker.upper()} Corporation',
            'industry': 'Various Industries',
            'sector': 'Mixed Sectors',
            'description': f'{ticker.upper()} is a publicly traded company. Detailed company information is not available in our database.',
            'market_cap': 'N/A',
            'employees': 'N/A',
            'founded': 'N/A',
            'headquarters': 'N/A',
            'key_products': 'Various products and services',
            'analyst_predictions': {
                'price_target': 'Not available',
                'rating': 'Not available',
                'growth_outlook': 'Consult financial advisors for investment guidance',
                'key_catalysts': 'Company-specific developments',
                'risks': 'Market volatility and company-specific risks'
            }
        }

def create_multi_ticker_price_chart(tickers, years_data_dict, num_years):
    """Create price history chart for multiple tickers"""
    fig = go.Figure()

    # Generate distinct colors for each ticker
    colors = generate_distinct_colors(len(tickers))

    for i, ticker in enumerate(tickers):
        years_data = years_data_dict.get(ticker, {})
        if not years_data:
            continue

        # Combine all years data for this ticker
        all_dates = []
        all_prices = []

        # Sort years and combine data
        sorted_years = sorted(years_data.keys())
        for year in sorted_years[-num_years:]:  # Last N years
            data = years_data[year]
            if data is not None and not data.empty:
                all_dates.extend(data.index)
                all_prices.extend(data['Close'])

        if all_dates and all_prices:
            # Create combined dataframe and sort by date
            combined_df = pd.DataFrame({
                'Date': all_dates,
                'Price': all_prices
            }).sort_values('Date')

            # Normalize prices to start at 100
            start_price = combined_df['Price'].iloc[0]
            normalized_prices = (combined_df['Price'] / start_price) * 100

            fig.add_trace(go.Scatter(
                x=combined_df['Date'],
                y=normalized_prices,
                mode='lines',
                name=ticker,
                line=dict(color=colors[i], width=3),
                hovertemplate=f'<b>{ticker}</b><br>' +
                             'Date: %{x}<br>' +
                             'Normalized Price: %{y:.1f}<br>' +
                             '<extra></extra>'
            ))

    # Add horizontal line at 100 (starting point)
    fig.add_hline(y=100, line_dash="dash", line_color="gray", opacity=0.5)

    fig.update_layout(
        title=f'Multi-Ticker Price Comparison - Last {num_years} Years (Normalized)',
        xaxis_title='Date',
        yaxis_title='Normalized Price (Start = 100)',
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=600
    )

    return fig

def create_multi_ticker_rsi_chart(tickers, years_data_dict, show_only_average=False):
    """Create seasonal RSI chart for multiple tickers"""
    fig = go.Figure()

    # Generate distinct colors for each ticker
    colors = generate_distinct_colors(len(tickers))

    for ticker_idx, ticker in enumerate(tickers):
        years_data = years_data_dict.get(ticker, {})
        if not years_data:
            continue

        # Sort years (oldest to newest)
        sorted_years = sorted(years_data.keys())

        # Calculate RSI for each year and collect monthly averages
        all_month_rsi = {}  # month -> list of RSI values

        for year in sorted_years:
            data = years_data[year]
            if data is not None and not data.empty:
                # Calculate RSI for this year
                rsi_values = calculate_rsi(data['Close'])

                # Add RSI column to data
                data_with_rsi = data.copy()
                data_with_rsi['RSI'] = rsi_values

                # Collect monthly RSI data
                for idx, row in data_with_rsi.iterrows():
                    month = idx.month
                    rsi_val = row['RSI']
                    if month not in all_month_rsi:
                        all_month_rsi[month] = []
                    all_month_rsi[month].append(rsi_val)

        if not all_month_rsi:
            continue

        # Calculate average RSI for each month
        avg_months = sorted(all_month_rsi.keys())
        avg_rsi = [np.mean(all_month_rsi[month]) for month in avg_months]

        # Convert months to day-of-year for plotting
        month_to_day = {1: 15, 2: 45, 3: 74, 4: 105, 5: 135, 6: 166,
                        7: 196, 8: 227, 9: 258, 10: 288, 11: 319, 12: 349}
        avg_days = [month_to_day[month] for month in avg_months]

        # Smooth the average RSI line
        if len(avg_days) > 3:
            from scipy.interpolate import interp1d
            f_avg = interp1d(avg_days, avg_rsi, kind='cubic', fill_value='extrapolate')
            smooth_days = np.linspace(1, 365, 365)
            smooth_rsi = f_avg(smooth_days)
        else:
            smooth_days = avg_days
            smooth_rsi = avg_rsi

        # Add average RSI line for this ticker
        fig.add_trace(go.Scatter(
            x=smooth_days,
            y=smooth_rsi,
            mode='lines',
            name=f'{ticker} RSI',
            line=dict(color=colors[ticker_idx], width=3),
            hovertemplate=f'<b>{ticker} RSI</b><br>' +
                         'Day of Year: %{x:.0f}<br>' +
                         'RSI: %{y:.1f}<br>' +
                         '<extra></extra>'
        ))

    # Add RSI reference lines
    fig.add_hline(y=70, line_dash="dash", line_color="red", opacity=0.7,
                  annotation_text="Overbought (70)", annotation_position="bottom right")
    fig.add_hline(y=30, line_dash="dash", line_color="green", opacity=0.7,
                  annotation_text="Oversold (30)", annotation_position="top right")
    fig.add_hline(y=50, line_dash="dot", line_color="gray", opacity=0.5,
                  annotation_text="Neutral (50)", annotation_position="top left")

    fig.update_layout(
        title=f'Multi-Ticker Seasonal RSI Comparison',
        xaxis_title='Day of Year',
        yaxis_title='RSI (Relative Strength Index)',
        yaxis=dict(range=[0, 100]),
        hovermode='closest',
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        ),
        height=600
    )

    # Add month markers
    month_days = [1, 32, 60, 91, 121, 152, 182, 213, 244, 274, 305, 335]
    month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

    fig.update_layout(
        xaxis=dict(
            tickmode='array',
            tickvals=month_days,
            ticktext=month_names
        )
    )

    return fig

def main():
    st.set_page_config(
        page_title="Stock Analysis Suite",
        page_icon="📈",
        layout="wide"
    )

    # Single page application - Seasonal Stock Analysis with multi-ticker support
    st.title("📈 Seasonal Stock Analysis")
    st.markdown("Analyze seasonal stock price movements for single stocks or compare multiple tickers")

    seasonal_analyzer_page()

def seasonal_analyzer_page():
    """Seasonal stock analysis page with multi-ticker support"""

    # Cache management section
    st.sidebar.subheader("📁 Cached Analyses")

    # Get all cached entries
    cached_entries = get_all_cached_entries()

    if cached_entries:
        st.sidebar.caption(f"Found {len(cached_entries)} cached analyses")

        # Display cached entries as clickable buttons
        for i, entry in enumerate(cached_entries[:10]):  # Show max 10 entries
            # Create display text based on entry type
            date_range = f"{entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}"

            if entry.get('type') == 'multi':
                # Multi-ticker entry
                ticker_count = len(entry['tickers'])
                button_text = f"🔄 {entry['ticker_display']} ({ticker_count} stocks, {entry['num_years']}y) {date_range}"
                help_text = f"Load cached multi-ticker analysis for {ticker_count} stocks with {entry['num_years']} years"
            else:
                # Single ticker entry
                button_text = f"📊 {entry['ticker']} ({entry['num_years']}y) {date_range}"
                help_text = f"Load cached analysis for {entry['ticker']} with {entry['num_years']} years"

            # Create unique key for button
            button_key = f"cache_load_{i}_{entry['cache_key'][:8]}"

            if st.sidebar.button(
                button_text,
                key=button_key,
                help=help_text
            ):
                if entry.get('type') == 'multi':
                    # Load multi-ticker cache
                    cached_data = load_multi_ticker_from_cache(
                        entry['tickers'],
                        entry['num_years'],
                        entry['start_month'],
                        entry['start_day'],
                        entry['end_month'],
                        entry['end_day']
                    )

                    if cached_data:
                        # Set session state for multi-ticker cache
                        st.session_state.load_cached_multi_data = cached_data
                        st.session_state.cached_tickers = entry['tickers']
                        st.session_state.cached_num_years = entry['num_years']
                        st.session_state.cached_start_month = entry['start_month']
                        st.session_state.cached_start_day = entry['start_day']
                        st.session_state.cached_end_month = entry['end_month']
                        st.session_state.cached_end_day = entry['end_day']
                        st.session_state.trigger_multi_cache_analysis = True
                        st.rerun()
                else:
                    # Load single ticker cache
                    cached_data = load_from_cache(
                        entry['ticker'],
                        entry['num_years'],
                        entry['start_month'],
                        entry['start_day'],
                        entry['end_month'],
                        entry['end_day']
                    )

                    if cached_data:
                        # Set session state to load cached data with same behavior as analyze button
                        st.session_state.load_cached_data = cached_data
                        st.session_state.cached_ticker = entry['ticker']
                        st.session_state.cached_num_years = entry['num_years']
                        st.session_state.cached_start_month = entry['start_month']
                        st.session_state.cached_start_day = entry['start_day']
                        st.session_state.cached_end_month = entry['end_month']
                        st.session_state.cached_end_day = entry['end_day']
                        st.session_state.trigger_cache_analysis = True  # Flag to trigger analysis display
                        st.rerun()

        if len(cached_entries) > 10:
            st.sidebar.caption(f"... and {len(cached_entries) - 10} more")

        # Clear cache button
        if st.sidebar.button("🗑️ Clear All Cache", help="Remove all cached analyses"):
            cleared_count = clear_cache()
            st.sidebar.success(f"Cleared {cleared_count} cached analyses")
            st.rerun()
    else:
        st.sidebar.caption("No cached analyses found")

    # Sidebar inputs
    st.sidebar.subheader("Analysis Settings")
    
    # Handle ticker input persistence - remember last used ticker(s)
    if 'reload_from_cache' in st.session_state and st.session_state.reload_from_cache:
        default_ticker = st.session_state.get('selected_ticker', '')
        st.session_state.reload_from_cache = False
    elif 'cached_ticker' in st.session_state:
        # Single ticker from cache
        default_ticker = st.session_state.cached_ticker
        # Remember this as the last used ticker
        st.session_state.last_used_ticker = st.session_state.cached_ticker
    elif 'cached_tickers' in st.session_state:
        # Multi-ticker cache - join with commas
        default_ticker = ', '.join(st.session_state.cached_tickers)
        # Remember this as the last used ticker(s)
        st.session_state.last_used_ticker = ', '.join(st.session_state.cached_tickers)
    else:
        # Use last used ticker if available, otherwise empty
        default_ticker = st.session_state.get('last_used_ticker', '')

    # Get default values from cache if available
    default_num_years = st.session_state.get('cached_num_years', 5)

    # Handle quick year selection
    if 'quick_years_selected' in st.session_state:
        default_num_years = st.session_state.quick_years_selected
        del st.session_state.quick_years_selected

    # Ticker input - supports single or multiple tickers
    ticker_input = st.sidebar.text_input(
        "Stock Ticker Symbol(s)",
        value=default_ticker,
        placeholder="Enter ticker(s) e.g., AAPL or AAPL, MSFT, GOOGL",
        help="Enter one or more stock ticker symbols separated by commas (e.g., AAPL or AAPL, MSFT, GOOGL for comparison)"
    ).upper()

    # Parse ticker input - detect single vs multiple tickers
    if ticker_input.strip():  # Only process if there's actual input
        # Remember this as the last used ticker for future sessions
        st.session_state.last_used_ticker = ticker_input

        if ',' in ticker_input:
            # Multiple tickers - split and clean
            tickers = [t.strip() for t in ticker_input.split(',') if t.strip()]

            # Limit to 10 tickers for performance
            if len(tickers) > 10:
                st.sidebar.warning("⚠️ Maximum 10 tickers allowed for performance reasons")
                tickers = tickers[:10]

            is_multi_ticker = True
            ticker = tickers[0] if tickers else ""  # Use first ticker for cache/form logic

            # Show ticker count
            if tickers:
                st.sidebar.caption(f"📊 Analyzing {len(tickers)} tickers: {', '.join(tickers)}")
        else:
            # Single ticker
            tickers = [ticker_input.strip()]
            is_multi_ticker = False
            ticker = tickers[0]
    else:
        # Empty input - no analysis possible
        tickers = []
        is_multi_ticker = False
        ticker = ""

    # Number of years
    num_years = st.sidebar.slider(
        "Number of Years to Compare",
        min_value=2,
        max_value=25,
        value=default_num_years,
        help="Select how many years of data to compare"
    )

    # Quick year selection buttons
    st.sidebar.caption("Quick Select:")
    col1, col2, col3 = st.sidebar.columns(3)

    with col1:
        if st.button("5 Years", key="quick_5_years", help="Set to 5 years"):
            st.session_state.quick_years_selected = 5
            st.rerun()

    with col2:
        if st.button("10 Years", key="quick_10_years", help="Set to 10 years"):
            st.session_state.quick_years_selected = 10
            st.rerun()

    with col3:
        if st.button("25 Years", key="quick_25_years", help="Set to 25 years"):
            st.session_state.quick_years_selected = 25
            st.rerun()
    
    # Date range selection
    st.sidebar.subheader("Date Range")
    
    # Handle month selection from cache
    if 'selected_start_month' in st.session_state:
        default_start_month = st.session_state.selected_start_month - 1  # Convert to 0-based index
        default_end_month = st.session_state.selected_end_month - 1
        # Clear the session state after using it
        del st.session_state.selected_start_month
        del st.session_state.selected_end_month
    elif 'cached_start_month' in st.session_state:
        default_start_month = st.session_state.cached_start_month - 1  # Convert to 0-based index
        default_end_month = st.session_state.cached_end_month - 1
    else:
        default_start_month = 0  # January
        default_end_month = 11   # December

    # Get default day values from cache if available
    default_start_day = st.session_state.get('cached_start_day', 1)
    default_end_day = st.session_state.get('cached_end_day', 31)

    # Start and end dates for the seasonal period
    col1, col2 = st.sidebar.columns(2)
    with col1:
        start_month = st.selectbox("Start Month",
                                 options=list(range(1, 13)),
                                 index=default_start_month,
                                 format_func=lambda x: datetime(2023, x, 1).strftime('%B'),
                                 key="seasonal_start_month")
        start_day = st.number_input("Start Day", min_value=1, max_value=31, value=default_start_day, key="seasonal_start_day")

    with col2:
        end_month = st.selectbox("End Month",
                               options=list(range(1, 13)),
                               index=default_end_month,
                               format_func=lambda x: datetime(2023, x, 1).strftime('%B'),
                               key="seasonal_end_month")
        end_day = st.number_input("End Day", min_value=1, max_value=31, value=default_end_day, key="seasonal_end_day")
    
    # Chart options
    st.sidebar.subheader("Chart Options")
    use_log_scale = st.sidebar.checkbox(
        "Use Logarithmic Scale for Price Chart",
        value=False,
        help="Logarithmic scale is useful for comparing percentage changes across different price levels"
    )

    show_only_average = st.sidebar.checkbox(
        "Show Only Average Line",
        value=False,
        help="Hide individual years and show only the smooth average trend"
    )



    # # Cache management section
    # st.sidebar.subheader("📁 Cached Analyses")

    # # Get all cached entries
    # cached_entries = get_all_cached_entries()

    # if cached_entries:
    #     st.sidebar.caption(f"Found {len(cached_entries)} cached analyses")

    #     # Display cached entries as clickable buttons
    #     for i, entry in enumerate(cached_entries[:10]):  # Show max 10 entries
    #         # Create display text
    #         date_range = f"{entry['start_month']}/{entry['start_day']} - {entry['end_month']}/{entry['end_day']}"
    #         button_text = f"{entry['ticker']} ({entry['num_years']}y) {date_range}"

    #         # Create unique key for button
    #         button_key = f"cache_load_{i}_{entry['cache_key'][:8]}"

    #         if st.sidebar.button(
    #             button_text,
    #             key=button_key,
    #             help=f"Load cached analysis for {entry['ticker']} with {entry['num_years']} years"
    #         ):
    #             # Load from cache and set session state
    #             cached_data = load_from_cache(
    #                 entry['ticker'],
    #                 entry['num_years'],
    #                 entry['start_month'],
    #                 entry['start_day'],
    #                 entry['end_month'],
    #                 entry['end_day']
    #             )

    #             if cached_data:
    #                 # Set session state to load cached data
    #                 st.session_state.load_cached_data = cached_data
    #                 st.session_state.cached_ticker = entry['ticker']
    #                 st.session_state.cached_num_years = entry['num_years']
    #                 st.session_state.cached_start_month = entry['start_month']
    #                 st.session_state.cached_start_day = entry['start_day']
    #                 st.session_state.cached_end_month = entry['end_month']
    #                 st.session_state.cached_end_day = entry['end_day']
    #                 st.rerun()

    #     if len(cached_entries) > 10:
    #         st.sidebar.caption(f"... and {len(cached_entries) - 10} more")

    #     # Clear cache button
    #     if st.sidebar.button("🗑️ Clear All Cache", help="Remove all cached analyses"):
    #         cleared_count = clear_cache()
    #         st.sidebar.success(f"Cleared {cleared_count} cached analyses")
    #         st.rerun()
    # else:
    #     st.sidebar.caption("No cached analyses found")

    # Analysis button - always required
    analyze_button = st.sidebar.button("Analyze Seasonal Patterns", type="primary")

    # Check if we should load cached data or trigger cache analysis
    load_cached = False
    load_multi_cached = False
    trigger_cache_analysis = st.session_state.get('trigger_cache_analysis', False)
    trigger_multi_cache_analysis = st.session_state.get('trigger_multi_cache_analysis', False)

    if 'load_cached_data' in st.session_state:
        cached_data = st.session_state.load_cached_data

        # Update form values to match cached data
        ticker = st.session_state.cached_ticker
        num_years = st.session_state.cached_num_years
        start_month = st.session_state.cached_start_month
        start_day = st.session_state.cached_start_day
        end_month = st.session_state.cached_end_month
        end_day = st.session_state.cached_end_day

        load_cached = True

        # Clear the session state
        del st.session_state.load_cached_data
        del st.session_state.cached_ticker
        del st.session_state.cached_num_years
        del st.session_state.cached_start_month
        del st.session_state.cached_start_day
        del st.session_state.cached_end_month
        del st.session_state.cached_end_day

    # Handle multi-ticker cache loading
    if 'load_cached_multi_data' in st.session_state:
        cached_multi_data = st.session_state.load_cached_multi_data
        load_multi_cached = True

        # Clear the session state
        del st.session_state.load_cached_multi_data
        del st.session_state.cached_tickers
        del st.session_state.cached_num_years
        del st.session_state.cached_start_month
        del st.session_state.cached_start_day
        del st.session_state.cached_end_month
        del st.session_state.cached_end_day

    # Clear trigger flags if they exist
    if 'trigger_cache_analysis' in st.session_state:
        del st.session_state.trigger_cache_analysis
    if 'trigger_multi_cache_analysis' in st.session_state:
        del st.session_state.trigger_multi_cache_analysis

    if analyze_button or load_cached or trigger_cache_analysis or load_multi_cached or trigger_multi_cache_analysis:
        if not ticker or not tickers:
            st.error("Please enter valid ticker symbol(s)")
            st.info("💡 **Examples:** AAPL, MSFT, GOOGL (single or multiple tickers separated by commas)")
            return

        # Display analysis type
        if is_multi_ticker:
            st.info(f"🔄 Multi-Ticker Analysis: {', '.join(tickers)} ({len(tickers)} stocks)")
        else:
            st.info(f"📊 Single Stock Analysis: {ticker}")

        # Check if we're loading from cache or triggered by cache button
        if load_cached or trigger_cache_analysis:
            # Use cached data with same loading experience as fresh analysis
            years_data = cached_data['years_data']

            # Show loading spinner for consistent UX (same as fresh data loading)
            with st.spinner(f"📁 Loading cached analysis for {ticker}..."):
                import time
                time.sleep(0.8)  # Brief pause for consistent loading experience

            st.success(f"✅ Loaded cached analysis for {ticker} ({len(years_data)} years)")
        elif load_multi_cached or trigger_multi_cache_analysis:
            # Use cached multi-ticker data with same loading display
            all_years_data = cached_multi_data['all_years_data']
            successful_tickers = list(all_years_data.keys())

            # Show loading spinner for consistent UX
            with st.spinner(f"📁 Loading cached multi-ticker analysis for {len(successful_tickers)} tickers..."):
                import time
                time.sleep(0.8)  # Brief pause for consistent loading experience

            st.success(f"✅ Loaded cached multi-ticker analysis ({len(successful_tickers)} tickers)")

            # Display multi-ticker charts directly from cache
            st.markdown("---")
            st.subheader("📈 Multi-Ticker Price Comparison")
            st.caption(f"💡 Normalized prices (all start at 100) • {len(successful_tickers)} tickers over {num_years} years")

            fig_multi_price = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
            if fig_multi_price:
                st.plotly_chart(fig_multi_price, use_container_width=True)

            st.subheader("📊 Multi-Ticker Seasonal RSI Comparison")
            st.caption(f"💡 RSI patterns • 70+ = Overbought, 30- = Oversold • Average trends across seasonal period")

            fig_multi_rsi = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
            if fig_multi_rsi:
                st.plotly_chart(fig_multi_rsi, use_container_width=True)

            # Summary table for cached multi-ticker
            st.subheader("📋 Multi-Ticker Summary")

            summary_data = []
            for ticker_name in successful_tickers:
                ticker_data = all_years_data[ticker_name]
                total_years = len(ticker_data)

                # Calculate average metrics across all years
                all_prices = []
                for year_data in ticker_data.values():
                    if year_data is not None and not year_data.empty:
                        all_prices.extend(year_data['Close'].tolist())

                if all_prices:
                    avg_price = np.mean(all_prices)
                    price_range = f"${min(all_prices):.2f} - ${max(all_prices):.2f}"
                    volatility = np.std(all_prices) / avg_price * 100
                else:
                    avg_price = 0
                    price_range = "N/A"
                    volatility = 0

                summary_data.append({
                    'Ticker': ticker_name,
                    'Years Loaded': total_years,
                    'Avg Price': f"${avg_price:.2f}",
                    'Price Range': price_range,
                    'Volatility (σ)': f"{volatility:.1f}%"
                })

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                st.dataframe(summary_df, use_container_width=True)

            return  # Exit early for cached multi-ticker analysis
        elif is_multi_ticker:
            # Multi-ticker analysis - fetch data for all tickers
            with st.spinner(f"🔄 Fetching data for {len(tickers)} tickers..."):
                all_years_data = {}
                failed_tickers = []

                # Progress tracking
                progress_bar = st.progress(0)
                status_text = st.empty()

                for i, current_ticker in enumerate(tickers):
                    status_text.text(f"Loading {current_ticker}... ({i+1}/{len(tickers)})")

                    # Fetch data for this ticker
                    ticker_years_data = {}
                    current_year = datetime.now().year

                    for j in range(num_years):
                        year = current_year - j

                        try:
                            start_date = datetime(year, start_month, start_day)
                            end_date = datetime(year, end_month, end_day)

                            # Handle year wrap-around
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, end_day)

                        except ValueError:
                            # Handle invalid dates
                            start_date = datetime(year, start_month, min(start_day, 28))
                            end_date = datetime(year, end_month, min(end_day, 28))
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, min(end_day, 28))

                        # Fetch data for this year
                        data = fetch_stock_data(current_ticker, start_date, end_date)

                        if data is not None and not data.empty:
                            normalized_data = normalize_to_day_of_year(data, year)
                            ticker_years_data[year] = normalized_data

                    if ticker_years_data:
                        all_years_data[current_ticker] = ticker_years_data
                    else:
                        failed_tickers.append(current_ticker)

                    progress_bar.progress((i + 1) / len(tickers))

                progress_bar.empty()
                status_text.empty()

                # Check results
                successful_tickers = list(all_years_data.keys())
                if not successful_tickers:
                    st.error(f"❌ No data found for any of the tickers: {', '.join(tickers)}")
                    return

                # Display results
                success_msg = f"✅ Successfully loaded data for {len(successful_tickers)} out of {len(tickers)} tickers"
                if failed_tickers:
                    success_msg += f" (failed: {', '.join(failed_tickers)})"
                st.success(success_msg)

                # Create multi-ticker charts
                st.markdown("---")
                st.subheader("📈 Multi-Ticker Price Comparison")
                st.caption(f"💡 Normalized prices (all start at 100) • {len(successful_tickers)} tickers over {num_years} years")

                fig_multi_price = create_multi_ticker_price_chart(successful_tickers, all_years_data, num_years)
                if fig_multi_price:
                    st.plotly_chart(fig_multi_price, use_container_width=True)

                st.subheader("📊 Multi-Ticker Seasonal RSI Comparison")
                st.caption(f"💡 RSI patterns • 70+ = Overbought, 30- = Oversold • Average trends across seasonal period")

                fig_multi_rsi = create_multi_ticker_rsi_chart(successful_tickers, all_years_data)
                if fig_multi_rsi:
                    st.plotly_chart(fig_multi_rsi, use_container_width=True)

                # Summary table for multi-ticker
                st.subheader("📋 Multi-Ticker Summary")

                summary_data = []
                for ticker_name in successful_tickers:
                    ticker_data = all_years_data[ticker_name]
                    total_years = len(ticker_data)

                    # Calculate average metrics across all years
                    all_prices = []
                    for year_data in ticker_data.values():
                        if year_data is not None and not year_data.empty:
                            all_prices.extend(year_data['Close'].tolist())

                    if all_prices:
                        avg_price = np.mean(all_prices)
                        price_range = f"${min(all_prices):.2f} - ${max(all_prices):.2f}"
                        volatility = np.std(all_prices) / avg_price * 100
                    else:
                        avg_price = 0
                        price_range = "N/A"
                        volatility = 0

                    summary_data.append({
                        'Ticker': ticker_name,
                        'Years Loaded': total_years,
                        'Avg Price': f"${avg_price:.2f}",
                        'Price Range': price_range,
                        'Volatility (σ)': f"{volatility:.1f}%"
                    })

                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    st.dataframe(summary_df, use_container_width=True)

                # Save multi-ticker analysis to cache
                if successful_tickers:
                    save_success = save_multi_ticker_to_cache(
                        successful_tickers,
                        num_years,
                        start_month,
                        start_day,
                        end_month,
                        end_day,
                        all_years_data
                    )
                    if save_success:
                        st.success(f"💾 Multi-ticker analysis saved to cache for future use")

                return  # Exit early for multi-ticker analysis
        else:
            # Check if data exists in cache first
            cached_analysis = load_from_cache(ticker, num_years, start_month, start_day, end_month, end_day)

            if cached_analysis:
                # Use cached data
                years_data = cached_analysis['years_data']
                st.info(f"📁 Found in cache: {ticker} with {num_years} years of data")
            else:
                # Fetch fresh data
                with st.spinner(f"🌐 Fetching fresh data for {ticker}..."):
                    current_year = datetime.now().year
                    years_data = {}
                    failed_years = []

                    # Progress bar
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    for i in range(num_years):
                        year = current_year - i  # Include current year
                        status_text.text(f"Loading data for {year}...")

                        # Create date range for this year
                        try:
                            start_date = datetime(year, start_month, start_day)
                            end_date = datetime(year, end_month, end_day)

                            # Handle year wrap-around (e.g., Nov to Feb)
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, end_day)

                        except ValueError:
                            # Handle invalid dates (e.g., Feb 31)
                            start_date = datetime(year, start_month, min(start_day, 28))
                            end_date = datetime(year, end_month, min(end_day, 28))
                            if end_date < start_date:
                                end_date = datetime(year + 1, end_month, min(end_day, 28))

                        # Fetch data for this year
                        data = fetch_stock_data(ticker, start_date, end_date)

                        if data is not None and not data.empty:
                            # Normalize to day of year
                            normalized_data = normalize_to_day_of_year(data, year)
                            years_data[year] = normalized_data
                        else:
                            failed_years.append(year)

                        progress_bar.progress((i + 1) / num_years)

                    progress_bar.empty()
                    status_text.empty()

                    # Save to cache if we successfully fetched data
                    if years_data:
                        save_success = save_to_cache(ticker, num_years, start_month, start_day, end_month, end_day, years_data)
                        if save_success:
                            st.success(f"💾 Data saved to cache for future use")


            
            if not years_data:
                st.error(f"❌ No data found for ticker {ticker} in the specified date range")
                if failed_years:
                    st.warning(f"Failed to fetch data for years: {', '.join(map(str, failed_years))}")
                st.info("""
                **Possible solutions:**
                - Check if the ticker symbol is correct (e.g., AAPL, MSFT, GOOGL)
                - Try a different date range
                - Wait a moment and try again (Yahoo Finance sometimes has rate limits)
                - Try a more popular stock ticker
                """)
                return

            # Display results for fresh data only
            if not (load_cached or trigger_cache_analysis):
                success_msg = f"✅ Successfully loaded data for {len(years_data)} years"
                # if failed_years:
                #     success_msg += f" (failed: {', '.join(map(str, failed_years))})"
                st.success(success_msg)

        # Check if we have data to display (applies to both cached and fresh data)
        if not years_data:
            st.error(f"❌ No data available for analysis")
            return

        # Display company information (applies to both cached and fresh data)
        company_info = get_company_info(ticker)

        st.markdown("---")
        col1, col2 = st.columns([2, 1])

        with col1:
            # Create clickable company name with MarketWatch link
            marketwatch_url = f"https://www.marketwatch.com/investing/stock/{ticker.lower()}"
            st.subheader(f"🏢 [{company_info['name']}]({marketwatch_url})")
            st.markdown(f"**Industry:** {company_info['industry']} | **Sector:** {company_info['sector']}")
            st.markdown(f"**Description:** {company_info['description']}")

            # Key business metrics
            st.markdown(f"**Key Products/Services:** {company_info['key_products']}")

        with col2:
            st.markdown("**Company Facts:**")
            st.markdown(f"• **Market Cap:** {company_info['market_cap']}")
            st.markdown(f"• **Employees:** {company_info['employees']}")
            st.markdown(f"• **Founded:** {company_info['founded']}")
            st.markdown(f"• **HQ:** {company_info['headquarters']}")

        # Analyst predictions section
        st.markdown("### 📊 Analyst Outlook & Predictions")
        pred = company_info['analyst_predictions']

        col1, col2, col3 = st.columns(3)
        with col1:
            st.markdown(f"**Price Target:** {pred['price_target']}")
            st.markdown(f"**Rating:** {pred['rating']}")

        with col2:
            st.markdown(f"**Growth Outlook:** {pred['growth_outlook']}")

        with col3:
            st.markdown(f"**Key Catalysts:** {pred['key_catalysts']}")
            st.markdown(f"**Key Risks:** {pred['risks']}")

        st.markdown("---")

        # Create and display charts (applies to both cached and fresh data)
        st.subheader("📈 Price Comparison")
        caption_text = "💡 Showing only average trend" if show_only_average else "💡 Each year in distinct colors • Thick gray line shows smooth monthly average trend"
        st.caption(caption_text)
        fig_price = create_seasonal_price_chart(ticker, years_data, use_log_scale, show_only_average)
        st.plotly_chart(fig_price, use_container_width=True)

        st.subheader("📊 Normalized Seasonal Patterns")
        caption_text_norm = "💡 All prices start at 100 for easier comparison • Shows relative performance" if not show_only_average else "💡 Normalized average trend starting at 100"
        st.caption(caption_text_norm)
        fig_normalized = create_normalized_seasonal_chart(ticker, years_data, show_only_average)
        st.plotly_chart(fig_normalized, use_container_width=True)

        st.subheader("📊 Multi-Year Price History")
        st.caption(f"💡 Continuous price line for last {min(num_years, len(years_data))} years")
        fig_multi_year = create_multi_year_line_chart(ticker, years_data, num_years)
        st.plotly_chart(fig_multi_year, use_container_width=True)

        st.subheader("📈 Seasonal RSI Analysis")
        caption_text_rsi = "💡 RSI momentum patterns • 70+ = Overbought, 30- = Oversold" if not show_only_average else "💡 Average RSI trend with overbought/oversold levels"
        st.caption(caption_text_rsi)
        fig_rsi = create_rsi_chart(ticker, years_data, show_only_average)
        st.plotly_chart(fig_rsi, use_container_width=True)

        # Recent year comparison chart
        current_year = datetime.now().year
        last_year = current_year - 1

        if last_year in years_data and current_year in years_data:
            st.subheader("🔍 Recent Year Focus")
            st.caption(f"💡 Direct comparison: {last_year} vs {current_year}")
            fig_recent = create_recent_comparison_chart(ticker, years_data)
            st.plotly_chart(fig_recent, use_container_width=True)
        elif current_year in years_data:
            st.subheader("🔍 Recent Year Focus")
            st.caption(f"💡 Only {current_year} data available for recent comparison")
            # Create chart with just current year
            recent_data = {current_year: years_data[current_year]}
            fig_recent = create_recent_comparison_chart(ticker, recent_data)
            st.plotly_chart(fig_recent, use_container_width=True)

        # Display summary statistics
        st.subheader("📋 Summary Statistics & Market Events")

        summary_data = []
        for year, data in years_data.items():
            if data is not None and not data.empty:
                start_price = data['Close'].iloc[0]
                end_price = data['Close'].iloc[-1]
                return_pct = ((end_price - start_price) / start_price) * 100
                volatility = data['Close'].pct_change().std() * np.sqrt(252) * 100
                max_price = data['Close'].max()
                min_price = data['Close'].min()
                max_drawdown = ((data['Close'] / data['Close'].cummax()) - 1).min() * 100
                market_events = get_ticker_specific_events(ticker, year)

                summary_data.append({
                    'Year': year,
                    'Start Price': f"${start_price:.2f}",
                    'End Price': f"${end_price:.2f}",
                    'Total Return': f"{return_pct:.2f}%",
                    'Max Price': f"${max_price:.2f}",
                    'Min Price': f"${min_price:.2f}",
                    'Max Drawdown': f"{max_drawdown:.2f}%",
                    'Volatility': f"{volatility:.2f}%",
                    'Key Events & Drivers': market_events
                })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)

            # Configure column display with text wrapping
            column_config = {
                'Key Events & Drivers': st.column_config.TextColumn(
                    'Key Events & Drivers',
                    width='large',
                    help=f'Major {ticker}-specific events and volatility drivers for the year'
                )
            }

            # Display the dataframe with custom styling for text wrapping
            st.markdown("""
            <style>
            .stDataFrame [data-testid="stDataFrameResizeHandle"] {
                display: none !important;
            }
            .stDataFrame td {
                white-space: normal !important;
                word-wrap: break-word !important;
                max-width: 300px !important;
                vertical-align: top !important;
            }
            .stDataFrame th {
                white-space: normal !important;
                word-wrap: break-word !important;
            }
            </style>
            """, unsafe_allow_html=True)

            st.dataframe(
                summary_df,
                use_container_width=True,
                column_config=column_config,
                height=600  # Make table even taller for better text display
            )
    
    # Instructions
    st.markdown("---")
    st.markdown("""
    ### How to Use:
    1. **Quick Access**: Click any cached analysis button in the sidebar to instantly reload
    2. **Single Stock Analysis**: Enter one ticker symbol (e.g., AAPL, MSFT, GOOGL)
    3. **Multi-Ticker Comparison**: Enter multiple tickers separated by commas (e.g., AAPL, MSFT, GOOGL)
    4. **Configure Analysis**:
       - Select the number of years to compare (2-25 years)
       - Choose the seasonal date range (start and end months/days)
       - Toggle chart options (logarithmic scale, show only average)
    5. Click "Analyze Seasonal Patterns" to load data and generate charts
    6. **Auto-Cache**: Single stock analyses are automatically saved for future quick access

    ### Analysis Types:

    **📊 Single Stock Analysis:**
    - Detailed seasonal price patterns with individual year lines
    - Normalized seasonal patterns (all prices start at 100)
    - Multi-year continuous price history
    - Seasonal RSI analysis with overbought/oversold levels
    - Recent year focus comparison
    - Company information and analyst predictions
    - Summary statistics with market events

    **🔄 Multi-Ticker Comparison:**
    - Normalized price comparison (all start at 100)
    - Seasonal RSI patterns for all tickers
    - Performance summary table
    - Up to 10 tickers simultaneously
    - Same seasonal date range for fair comparison

    ### Key Features:
    - **Integrated Analysis**: Single and multi-ticker analysis on one page
    - **Permanent Cache System**: Saves single stock analyses with clickable sidebar buttons
    - **Canadian Exchange Support**: Automatic detection of TSX (.TO), TSX-V (.V), CSE (.CN), NEO (.NE)
    - **Flexible Input**: Automatically detects single vs multiple ticker input
    - **Performance Optimized**: Multi-ticker analysis limited to 10 stocks for optimal performance
    - **Consistent UX**: Same loading experience for cached and fresh data

    ### Example Inputs:
    - **Single Stock**: `AAPL` or `SHOP.TO` or `DEMO`
    - **Tech Comparison**: `AAPL, MSFT, GOOGL, AMZN, META`
    - **Canadian Banks**: `RY.TO, TD.TO, BMO.TO, BNS.TO, CM.TO`
    - **Energy Sector**: `XOM, CVX, COP, EOG, SLB`
    - **Mixed Portfolio**: `AAPL, SHOP.TO, TSLA, NVDA`
    """)

if __name__ == "__main__":
    main()
