#!/usr/bin/env python3
"""
Test script for ETF Analyzer functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import get_etf_holdings, create_etf_constituents_chart
from datetime import datetime

def test_etf_holdings():
    """Test ETF holdings data"""
    print("Testing ETF Holdings Data")
    print("=" * 40)
    
    # Test supported ETFs
    supported_etfs = ['SPY', 'QQQ', 'VTI', 'VEQT']
    
    for etf in supported_etfs:
        holdings = get_etf_holdings(etf)
        print(f"\n{etf}:")
        print(f"  Holdings count: {len(holdings)}")
        
        if holdings:
            total_weight = sum(h['weight'] for h in holdings)
            print(f"  Total weight: {total_weight:.1f}%")
            print(f"  Top 3 holdings:")
            for i, holding in enumerate(holdings[:3]):
                print(f"    {i+1}. {holding['symbol']} ({holding['weight']}%) - {holding['name']}")
        else:
            print(f"  ❌ No holdings found for {etf}")
    
    # Test unsupported ETF
    unsupported = get_etf_holdings('UNKNOWN')
    print(f"\nUNKNOWN ETF: {len(unsupported)} holdings (should be 0)")
    
    print("\n✅ ETF holdings test completed")

def test_etf_chart_creation():
    """Test ETF constituent chart creation"""
    print("\nTesting ETF Chart Creation")
    print("=" * 40)
    
    # Test with SPY
    etf_ticker = 'SPY'
    holdings = get_etf_holdings(etf_ticker)
    num_years = 2  # Use shorter period for testing
    
    print(f"\nCreating chart for {etf_ticker} with {len(holdings)} holdings...")
    print(f"Analysis period: {num_years} years")
    
    try:
        fig, successful_tickers, failed_tickers = create_etf_constituents_chart(
            etf_ticker, holdings, num_years
        )
        
        if fig:
            print(f"✅ Chart created successfully")
            print(f"  Chart title: {fig.layout.title.text}")
            print(f"  Number of traces: {len(fig.data)}")
            print(f"  Successful tickers: {len(successful_tickers)}")
            print(f"  Failed tickers: {len(failed_tickers)}")
            
            if successful_tickers:
                print(f"  Successfully loaded: {', '.join(successful_tickers[:5])}")
                if len(successful_tickers) > 5:
                    print(f"    ... and {len(successful_tickers) - 5} more")
            
            if failed_tickers:
                print(f"  Failed to load: {', '.join(failed_tickers)}")
            
            # Check chart properties
            if len(fig.data) > 0:
                sample_trace = fig.data[0]
                print(f"  Sample trace name: {sample_trace.name}")
                print(f"  Sample trace type: {sample_trace.type}")
                
            return True
        else:
            print("❌ Chart creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error creating chart: {e}")
        return False

def test_etf_data_quality():
    """Test data quality for ETF holdings"""
    print("\nTesting ETF Data Quality")
    print("=" * 40)
    
    etfs_to_test = ['SPY', 'QQQ']
    
    for etf in etfs_to_test:
        print(f"\nTesting {etf} data quality...")
        holdings = get_etf_holdings(etf)
        
        # Check data structure
        for i, holding in enumerate(holdings[:3]):  # Test first 3
            required_fields = ['symbol', 'weight', 'name']
            missing_fields = [field for field in required_fields if field not in holding]
            
            if missing_fields:
                print(f"  ❌ Holding {i+1} missing fields: {missing_fields}")
            else:
                print(f"  ✅ Holding {i+1}: {holding['symbol']} - all fields present")
                
                # Check data types
                if not isinstance(holding['weight'], (int, float)):
                    print(f"    ⚠️ Weight is not numeric: {type(holding['weight'])}")
                if not isinstance(holding['symbol'], str):
                    print(f"    ⚠️ Symbol is not string: {type(holding['symbol'])}")
                if not isinstance(holding['name'], str):
                    print(f"    ⚠️ Name is not string: {type(holding['name'])}")
        
        # Check weight totals
        total_weight = sum(h['weight'] for h in holdings)
        print(f"  Total weight: {total_weight:.1f}%")
        
        if total_weight > 100:
            print(f"  ⚠️ Total weight exceeds 100%")
        elif total_weight < 10:
            print(f"  ⚠️ Total weight seems too low")
        else:
            print(f"  ✅ Weight total looks reasonable")

def main():
    """Run all ETF analyzer tests"""
    print("ETF Analyzer Test Suite")
    print("=" * 50)
    
    # Run tests
    test_etf_holdings()
    test_etf_chart_creation()
    test_etf_data_quality()
    
    print("\n" + "=" * 50)
    print("✅ ETF Analyzer Tests Complete!")
    print("=" * 50)
    
    print("\nETF Analyzer Features:")
    print("• Supports 4 major ETFs (SPY, QQQ, VTI, VEQT)")
    print("• Shows top 10 holdings for each ETF")
    print("• Normalizes all prices to start at 100")
    print("• Displays constituent performance over 1-10 years")
    print("• Includes Canadian stocks for VEQT")
    print("• Shows weight percentages in legend")
    print("• Provides detailed holdings table")
    
    print("\nSupported ETFs:")
    print("• SPY: SPDR S&P 500 ETF Trust")
    print("• QQQ: Invesco QQQ Trust (Nasdaq-100)")
    print("• VTI: Vanguard Total Stock Market ETF")
    print("• VEQT: Vanguard All Equity ETF (Canadian)")
    
    print("\nUsage:")
    print("1. Select 'ETF Constituent Analysis' from sidebar")
    print("2. Enter ETF ticker (SPY, QQQ, VTI, VEQT)")
    print("3. Choose number of years (1-10)")
    print("4. Click 'Analyze ETF Constituents'")
    print("5. View normalized price performance chart")
    print("6. Review holdings table with success/failure status")

if __name__ == "__main__":
    main()
